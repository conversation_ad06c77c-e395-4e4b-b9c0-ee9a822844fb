import { Component, DestroyRef, inject, input, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PHONE_CODES } from ':core/constants';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { DigitsValidator, DigitsValidatorErrors } from ':shared/validators/digits.validator';

type PhoneCode = (typeof PHONE_CODES)[number];

@Component({
    selector: 'app-phone-input',
    standalone: true,
    templateUrl: './phone-input.component.html',
    styleUrls: ['./phone-input.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, SelectComponent, InputTextComponent, TranslateModule],
})
export class PhoneInputComponent implements OnInit {
    readonly parentForm = input.required<FormGroup>();

    readonly initialPhone = input<Partial<{ prefix: number | null; digits: number | null }> | undefined>({});

    readonly idPrefix = input<string | undefined>();
    readonly testId = input<string | undefined>();

    readonly PHONE_CODES = [...PHONE_CODES];

    digitsErrorMessage: WritableSignal<string | undefined> = signal(undefined);
    formattedPrefix: WritableSignal<string> = signal('');
    isInitialized = signal(false);
    private readonly _destroyRef = inject(DestroyRef);

    get phoneGroup(): FormGroup {
        return this.parentForm().get('phone') as FormGroup;
    }

    constructor(private readonly _translateService: TranslateService) {}

    ngOnInit(): void {
        const init = this.initialPhone();
        const defaultPrefix = init?.prefix
            ? (this.PHONE_CODES.find((pc) => pc.code === init.prefix!) ?? this.PHONE_CODES[0])
            : this.PHONE_CODES[0];
        this._updateFormattedPrefix(defaultPrefix);
        const defaultDigits = init?.digits != null ? parseInt(String(init.digits), 10) : null;

        const group = new FormGroup({
            prefix: new FormControl<PhoneCode | null>(defaultPrefix),
            digits: new FormControl<number | null>(defaultDigits, [DigitsValidator(0, 15)]),
        });

        this.parentForm().setControl('phone', group);

        group
            .get('prefix')
            ?.valueChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe((val) => {
                this._updateFormattedPrefix(val);
            });

        group
            .get('digits')
            ?.statusChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe(() => {
                switch ((group.get('digits')?.errors as any)?.error) {
                    case DigitsValidatorErrors.INVALID_LENGTH:
                        this.digitsErrorMessage.set(this._translateService.instant('admin.users.phone_length_error'));
                        break;
                    case DigitsValidatorErrors.INVALID_DIGITS:
                        this.digitsErrorMessage.set(this._translateService.instant('admin.users.phone_format_error'));
                        break;
                    default:
                        this.digitsErrorMessage.set(undefined);
                        break;
                }
            });
        group.get('digits')?.valueChanges.subscribe((val) => {
            console.log(val);
        });

        this.isInitialized.set(true);
    }

    private _updateFormattedPrefix(phoneCode: PhoneCode | null): void {
        this.formattedPrefix.set(`+${phoneCode?.code ?? ''}`);
    }

    phoneCodesDisplayWith(phoneCode: any): string {
        return phoneCode?.text || '';
    }
}
